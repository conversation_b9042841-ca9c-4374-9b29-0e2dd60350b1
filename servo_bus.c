/*
 * 总线舵机驱动实现文件
 * 支持LX-16A、LX-224等总线舵机
 * 
 * 通信协议：
 * 发送：0x55 0x55 ID LEN CMD PARAM1 PARAM2 ... CHECKSUM
 * 接收：0x55 0x55 ID LEN CMD PARAM1 PARAM2 ... CHECKSUM
 */

#include "servo_bus.h"
#include <string.h>

// 全局变量
static uint32_t system_tick_ms = 0;
static servo_status_t servo_status[3];  // 索引0不使用，1和2对应舵机1和2

// 内部函数声明
static servo_error_t servo_send_command(uint8_t id, uint8_t cmd, uint8_t *params, uint8_t param_len);
static servo_error_t servo_receive_response(uint8_t id, uint8_t cmd, uint8_t *data, uint8_t *data_len);
static uint8_t calculate_checksum(uint8_t *data, uint8_t len);
static void uart_send_byte(uint8_t id, uint8_t data);
static uint8_t uart_receive_byte(uint8_t id, uint32_t timeout_ms);
static bool uart_data_available(uint8_t id);

/**
 * @brief 初始化总线舵机系统
 */
servo_error_t servo_bus_init(void)
{
    // 初始化舵机状态
    memset(servo_status, 0, sizeof(servo_status));
    
    // 设置默认参数
    for (int i = 1; i <= 2; i++)
    {
        servo_status[i].id = i;
        servo_status[i].position = 500;  // 中间位置
        servo_status[i].target = 500;
        servo_status[i].speed = 500;
        servo_status[i].is_moving = false;
        servo_status[i].load_status = true;
    }
    
    // 等待舵机启动
    delay_ms(100);
    
    return SERVO_OK;
}

/**
 * @brief 设置舵机角度
 */
servo_error_t servo_set_angle(uint8_t id, uint16_t angle)
{
    return servo_set_angle_with_time(id, angle, SERVO_DEFAULT_TIME);
}

/**
 * @brief 设置舵机角度和运动时间
 */
servo_error_t servo_set_angle_with_time(uint8_t id, uint16_t angle, uint16_t time)
{
    if (id < 1 || id > 2)
        return SERVO_ERROR_INVALID_ID;
    
    if (angle > SERVO_ANGLE_MAX)
        return SERVO_ERROR_INVALID_PARAM;
    
    uint16_t position = angle_to_position(angle);
    uint8_t params[4];
    
    // 参数：位置低字节，位置高字节，时间低字节，时间高字节
    params[0] = position & 0xFF;
    params[1] = (position >> 8) & 0xFF;
    params[2] = time & 0xFF;
    params[3] = (time >> 8) & 0xFF;
    
    servo_error_t result = servo_send_command(id, SERVO_CMD_MOVE_TIME_WRITE, params, 4);
    
    if (result == SERVO_OK)
    {
        servo_status[id].target = position;
        servo_status[id].is_moving = true;
    }
    
    return result;
}

/**
 * @brief 设置舵机角度和运动速度
 */
servo_error_t servo_set_angle_with_speed(uint8_t id, uint16_t angle, uint16_t speed)
{
    if (speed < SERVO_SPEED_MIN || speed > SERVO_SPEED_MAX)
        return SERVO_ERROR_INVALID_PARAM;
    
    // 根据速度计算时间（简化计算）
    uint16_t current_angle = position_to_angle(servo_status[id].position);
    uint16_t angle_diff = (angle > current_angle) ? (angle - current_angle) : (current_angle - angle);
    uint16_t time = (angle_diff * 1000) / speed;  // 时间 = 角度差 / 速度 * 1000ms
    
    if (time < 20) time = 20;  // 最小时间20ms
    if (time > 30000) time = 30000;  // 最大时间30s
    
    return servo_set_angle_with_time(id, angle, time);
}

/**
 * @brief 读取舵机当前位置
 */
uint16_t servo_read_position(uint8_t id)
{
    if (id < 1 || id > 2)
        return 0xFFFF;
    
    uint8_t response_data[8];
    uint8_t response_len = 0;
    
    servo_error_t result = servo_send_command(id, SERVO_CMD_POS_READ, NULL, 0);
    if (result != SERVO_OK)
        return 0xFFFF;
    
    result = servo_receive_response(id, SERVO_CMD_POS_READ, response_data, &response_len);
    if (result != SERVO_OK || response_len < 2)
        return 0xFFFF;
    
    uint16_t position = response_data[0] | (response_data[1] << 8);
    servo_status[id].position = position;
    
    return position_to_angle(position);
}

/**
 * @brief 停止舵机运动
 */
servo_error_t servo_stop(uint8_t id)
{
    if (id < 1 || id > 2)
        return SERVO_ERROR_INVALID_ID;
    
    servo_error_t result = servo_send_command(id, SERVO_CMD_MOVE_STOP, NULL, 0);
    
    if (result == SERVO_OK)
    {
        servo_status[id].is_moving = false;
    }
    
    return result;
}

/**
 * @brief 开始舵机运动
 */
servo_error_t servo_start(uint8_t id)
{
    if (id < 1 || id > 2)
        return SERVO_ERROR_INVALID_ID;
    
    return servo_send_command(id, SERVO_CMD_MOVE_START, NULL, 0);
}

/**
 * @brief 设置舵机力矩开关
 */
servo_error_t servo_set_torque(uint8_t id, bool enable)
{
    if (id < 1 || id > 2)
        return SERVO_ERROR_INVALID_ID;
    
    uint8_t param = enable ? 1 : 0;
    servo_error_t result = servo_send_command(id, SERVO_CMD_LOAD_OR_UNLOAD_WRITE, &param, 1);
    
    if (result == SERVO_OK)
    {
        servo_status[id].load_status = enable;
    }
    
    return result;
}

/**
 * @brief 设置舵机LED状态
 */
servo_error_t servo_set_led(uint8_t id, bool enable)
{
    if (id < 1 || id > 2)
        return SERVO_ERROR_INVALID_ID;
    
    uint8_t param = enable ? 1 : 0;
    return servo_send_command(id, SERVO_CMD_LED_CTRL_WRITE, &param, 1);
}

/**
 * @brief 角度转换为舵机位置值
 */
uint16_t angle_to_position(uint16_t angle)
{
    if (angle > 180) angle = 180;
    // 0度对应125，180度对应875 (LX-16A标准)
    return 125 + (angle * 750) / 180;
}

/**
 * @brief 舵机位置值转换为角度
 */
uint16_t position_to_angle(uint16_t position)
{
    if (position < 125) position = 125;
    if (position > 875) position = 875;
    // 125对应0度，875对应180度
    return ((position - 125) * 180) / 750;
}

/**
 * @brief 发送舵机命令
 */
static servo_error_t servo_send_command(uint8_t id, uint8_t cmd, uint8_t *params, uint8_t param_len)
{
    uint8_t frame[16];
    uint8_t frame_len = 0;
    
    // 构建数据帧
    frame[frame_len++] = SERVO_FRAME_HEADER;  // 帧头1
    frame[frame_len++] = SERVO_FRAME_HEADER;  // 帧头2
    frame[frame_len++] = id;                  // 舵机ID
    frame[frame_len++] = param_len + 3;       // 长度 = 参数长度 + ID + CMD + CHECKSUM
    frame[frame_len++] = cmd;                 // 命令
    
    // 添加参数
    for (uint8_t i = 0; i < param_len; i++)
    {
        frame[frame_len++] = params[i];
    }
    
    // 计算校验和
    uint8_t checksum = calculate_checksum(&frame[2], frame_len - 2);
    frame[frame_len++] = checksum;
    
    // 发送数据
    for (uint8_t i = 0; i < frame_len; i++)
    {
        uart_send_byte(id, frame[i]);
    }
    
    return SERVO_OK;
}

/**
 * @brief 接收舵机响应
 */
static servo_error_t servo_receive_response(uint8_t id, uint8_t cmd, uint8_t *data, uint8_t *data_len)
{
    uint8_t frame[16];
    uint8_t frame_len = 0;
    uint32_t start_time = get_systick_ms();
    
    *data_len = 0;
    
    // 等待帧头
    while (get_systick_ms() - start_time < SERVO_TIMEOUT_MS)
    {
        if (uart_data_available(id))
        {
            uint8_t byte = uart_receive_byte(id, 10);
            if (byte == SERVO_FRAME_HEADER)
            {
                frame[frame_len++] = byte;
                if (frame_len >= 2) break;
            }
            else
            {
                frame_len = 0;
            }
        }
    }
    
    if (frame_len < 2)
        return SERVO_ERROR_TIMEOUT;
    
    // 接收剩余数据
    while (frame_len < sizeof(frame) && get_systick_ms() - start_time < SERVO_TIMEOUT_MS)
    {
        if (uart_data_available(id))
        {
            frame[frame_len++] = uart_receive_byte(id, 10);
            
            // 检查是否接收完整
            if (frame_len >= 4)
            {
                uint8_t expected_len = frame[3] + 3;  // 数据长度 + 帧头(2) + ID(1)
                if (frame_len >= expected_len)
                    break;
            }
        }
    }
    
    if (frame_len < 6)
        return SERVO_ERROR_TIMEOUT;
    
    // 验证校验和
    uint8_t checksum = calculate_checksum(&frame[2], frame_len - 3);
    if (checksum != frame[frame_len - 1])
        return SERVO_ERROR_CHECKSUM;
    
    // 提取数据
    uint8_t param_len = frame[3] - 3;  // 总长度 - ID - CMD - CHECKSUM
    if (param_len > 0)
    {
        memcpy(data, &frame[5], param_len);
        *data_len = param_len;
    }
    
    return SERVO_OK;
}

/**
 * @brief 计算校验和
 */
static uint8_t calculate_checksum(uint8_t *data, uint8_t len)
{
    uint8_t checksum = 0;
    for (uint8_t i = 0; i < len; i++)
    {
        checksum += data[i];
    }
    return ~checksum;  // 取反
}

/**
 * @brief 发送一个字节到指定舵机
 */
static void uart_send_byte(uint8_t id, uint8_t data)
{
    if (id == 1)
    {
        // 舵机1使用UART0
        while (!DL_UART_Main_isTXFIFOEmpty(UART_0_INST));
        DL_UART_Main_transmitData(UART_0_INST, data);
    }
    else if (id == 2)
    {
        // 舵机2使用UART1
        while (!DL_UART_Main_isTXFIFOEmpty(UART_1_INST));
        DL_UART_Main_transmitData(UART_1_INST, data);
    }
}

/**
 * @brief 从指定舵机接收一个字节
 */
static uint8_t uart_receive_byte(uint8_t id, uint32_t timeout_ms)
{
    uint32_t start_time = get_systick_ms();

    while (get_systick_ms() - start_time < timeout_ms)
    {
        if (id == 1 && DL_UART_Main_isRXFIFOEmpty(UART_0_INST) == false)
        {
            return DL_UART_Main_receiveData(UART_0_INST);
        }
        else if (id == 2 && DL_UART_Main_isRXFIFOEmpty(UART_1_INST) == false)
        {
            return DL_UART_Main_receiveData(UART_1_INST);
        }
    }

    return 0;  // 超时返回0
}

/**
 * @brief 检查指定舵机是否有数据可读
 */
static bool uart_data_available(uint8_t id)
{
    if (id == 1)
    {
        return DL_UART_Main_isRXFIFOEmpty(UART_0_INST) == false;
    }
    else if (id == 2)
    {
        return DL_UART_Main_isRXFIFOEmpty(UART_1_INST) == false;
    }

    return false;
}

/**
 * @brief 获取系统时间戳
 */
uint32_t get_systick_ms(void)
{
    return system_tick_ms;
}

/**
 * @brief 延时函数
 */
void delay_ms(uint32_t ms)
{
    uint32_t start = get_systick_ms();
    while (get_systick_ms() - start < ms)
    {
        // 等待
    }
}

/**
 * @brief 系统滴答定时器中断处理函数
 * 需要在1ms定时器中断中调用此函数
 */
void servo_systick_handler(void)
{
    system_tick_ms++;
}
