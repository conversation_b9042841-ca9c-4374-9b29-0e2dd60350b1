# CCS编译错误修复指南

## 错误分析
```
#10056: symbol "TIMA0_IRQHandler" redefined: 
first defined in "./empty.o"; 
redefined in "./mg996r_example.o"
```

这个错误表明项目中有重复的中断处理函数定义。

## 解决步骤

### 步骤1: 清理项目文件

#### 1.1 检查项目中的文件
在CCS项目中，确保只包含以下必需文件：
```
✅ 保留这些文件:
├── empty.c              # 主程序文件
├── empty.syscfg         # SysConfig配置
├── servo_bus.h          # 舵机驱动头文件
├── servo_bus.c          # 舵机驱动实现
├── ti_msp_dl_config.h   # TI配置头文件
├── ti_msp_dl_config.c   # TI配置实现
├── .ccsproject          # CCS项目配置
└── .project             # Eclipse项目配置

❌ 删除这些文件:
├── mg996r_example.c     # 删除示例文件
├── main.c               # 删除默认main文件
├── launch.json          # 删除VS Code配置
└── 其他示例文件
```

#### 1.2 删除冲突文件
1. 在CCS项目浏览器中右键 `mg996r_example.c`
2. 选择 "Delete"
3. 选择 "Delete project contents on disk"

### 步骤2: 修正中断处理函数

#### 2.1 检查empty.c中的中断函数
确保empty.c中只包含需要的中断处理函数：

```c
// 正确的中断处理函数定义
void UART_0_INST_IRQHandler(void)
{
    switch (DL_UART_Main_getPendingInterrupt(UART_0_INST))
    {
        case DL_UART_MAIN_IIDX_RX:
        {
            uint8_t received_char = DL_UART_Main_receiveData(UART_0_INST);
            // 处理接收到的字符
            break;
        }
        default:
            break;
    }
}

void TIMER_0_INST_IRQHandler(void)
{
    switch (DL_TimerG_getPendingInterrupt(TIMER_0_INST))
    {
        case DL_TIMER_IIDX_ZERO:
            servo_systick_handler();
            break;
        default:
            break;
    }
}
```

#### 2.2 删除不需要的中断函数
如果empty.c中有`TIMA0_IRQHandler`，请删除它：
```c
// 删除这个函数（如果存在）
void TIMA0_IRQHandler(void)
{
    // 删除整个函数
}
```

### 步骤3: 重新配置SysConfig

#### 3.1 打开SysConfig
1. 双击 `empty.syscfg`
2. 检查Timer配置

#### 3.2 确认Timer配置
确保只配置了需要的Timer：
- ✅ TIMER_0 (TIMG0) - 用于系统滴答
- ❌ 删除其他Timer配置

#### 3.3 重新生成代码
1. 在SysConfig中点击 "Generate"
2. 或者保存文件自动生成

### 步骤4: 清理编译文件

#### 4.1 清理项目
1. 在CCS中：Project → Clean
2. 选择当前项目
3. 点击 "Clean"

#### 4.2 删除编译输出
手动删除以下目录（如果存在）：
```
Debug/
Release/
.launches/
```

### 步骤5: 重新编译

#### 5.1 编译项目
1. Project → Build Project (Ctrl+B)
2. 检查编译输出

#### 5.2 验证编译成功
编译成功后应该看到：
```
**** Build Finished ****
```

## 完整的项目清理脚本

### Windows批处理脚本
创建 `clean_project.bat`：
```batch
@echo off
echo 清理CCS项目...

REM 删除编译输出
if exist Debug rmdir /s /q Debug
if exist Release rmdir /s /q Release
if exist .launches rmdir /s /q .launches

REM 删除冲突文件
if exist mg996r_example.c del mg996r_example.c
if exist main.c del main.c
if exist launch.json del launch.json
if exist .vscode rmdir /s /q .vscode

echo 项目清理完成！
pause
```

### Linux/Mac脚本
创建 `clean_project.sh`：
```bash
#!/bin/bash
echo "清理CCS项目..."

# 删除编译输出
rm -rf Debug/
rm -rf Release/
rm -rf .launches/

# 删除冲突文件
rm -f mg996r_example.c
rm -f main.c
rm -f launch.json
rm -rf .vscode/

echo "项目清理完成！"
```

## 预防措施

### 1. 项目文件管理
- 只保留必需的源文件
- 不要混合不同的示例代码
- 使用版本控制跟踪文件变化

### 2. 中断函数命名
- 确保中断函数名称与SysConfig生成的一致
- 不要手动定义未使用的中断函数
- 遵循TI的命名规范

### 3. SysConfig使用
- 通过SysConfig配置所有外设
- 让SysConfig生成中断处理函数框架
- 不要手动修改生成的配置文件

## 常见错误和解决方法

### 错误1: 找不到中断函数
```
undefined reference to 'TIMER_0_INST_IRQHandler'
```
**解决**: 确保在empty.c中定义了对应的中断处理函数

### 错误2: 重复定义
```
symbol "XXX_IRQHandler" redefined
```
**解决**: 删除重复的函数定义，只保留一个

### 错误3: SysConfig生成失败
```
SysConfig generation failed
```
**解决**: 检查.syscfg文件语法，重新配置外设

## 验证步骤

### 1. 编译验证
```bash
# 应该看到成功信息
**** Build Finished ****
```

### 2. 文件验证
确保项目只包含必需文件：
- empty.c ✅
- servo_bus.c ✅
- ti_msp_dl_config.c ✅
- 没有mg996r_example.c ❌

### 3. 功能验证
1. 下载程序到开发板
2. 连接串口查看输出
3. 测试舵机控制命令

## 如果问题仍然存在

### 最后的解决方案：重新创建项目
1. 创建新的CCS项目
2. 只复制必需的源文件
3. 重新配置SysConfig
4. 逐个添加功能进行测试

这样可以确保项目的干净和正确性。
