/*
 * 开发板硬件抽象层头文件
 * 包含所有硬件相关的定义和函数声明
 */

#ifndef __BOARD_H__
#define __BOARD_H__

#include "ti_msp_dl_config.h"
#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>

// 系统时钟频率
#define SYSTEM_CLOCK_FREQ   32000000UL

// LED定义
#define LED_PORT            GPIOA
#define LED_PIN             DL_GPIO_PIN_0

// 按键定义
#define KEY_PORT            GPIOB
#define KEY_PIN             DL_GPIO_PIN_21

// UART定义
#define DEBUG_UART          UART_0_INST
#define SERVO1_UART         UART_0_INST  // 舵机1使用UART0
#define SERVO2_UART         UART_1_INST  // 舵机2使用UART1

// 舵机UART引脚定义
// 舵机1 - UART0
#define SERVO1_TX_PORT      GPIOA
#define SERVO1_TX_PIN       DL_GPIO_PIN_10
#define SERVO1_RX_PORT      GPIOA
#define SERVO1_RX_PIN       DL_GPIO_PIN_11

// 舵机2 - UART1
#define SERVO2_TX_PORT      GPIOA
#define SERVO2_TX_PIN       DL_GPIO_PIN_8
#define SERVO2_RX_PORT      GPIOA
#define SERVO2_RX_PIN       DL_GPIO_PIN_9

// 函数声明

/**
 * @brief 板级初始化
 */
void board_init(void);

/**
 * @brief LED控制
 * @param state true-点亮，false-熄灭
 */
void led_set(bool state);

/**
 * @brief LED切换状态
 */
void led_toggle(void);

/**
 * @brief 读取按键状态
 * @return true-按下，false-释放
 */
bool key_read(void);

/**
 * @brief 延时函数(毫秒)
 * @param ms 延时毫秒数
 */
void delay_ms(uint32_t ms);

/**
 * @brief 延时函数(微秒)
 * @param us 延时微秒数
 */
void delay_us(uint32_t us);

/**
 * @brief 延时函数(1毫秒)
 */
void delay_1ms(void);

/**
 * @brief printf重定向到UART
 */
int lc_printf(const char *format, ...);

/**
 * @brief 获取系统运行时间(毫秒)
 * @return 系统运行时间
 */
uint32_t get_tick_ms(void);

/**
 * @brief 系统滴答中断处理函数
 * 在定时器中断中调用
 */
void systick_handler(void);

#endif /* __BOARD_H__ */
