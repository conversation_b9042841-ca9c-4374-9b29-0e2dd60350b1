/*
 * TI MSPM0G3507 配置实现文件
 * 此文件通常由SysConfig工具生成，这里提供基本配置实现
 */

#include "ti_msp_dl_config.h"

/**
 * @brief 系统初始化
 */
void SYSCFG_DL_init(void)
{
    SYSCFG_DL_initPower();
    SYSCFG_DL_GPIO_init();
    SYSCFG_DL_SYSCTL_init();
    SYSCFG_DL_UART_0_init();
    SYSCFG_DL_UART_1_init();
    SYSCFG_DL_TIMER_0_init();
}

/**
 * @brief 电源初始化
 */
void SYSCFG_DL_initPower(void)
{
    DL_GPIO_reset(GPIOA);
    DL_GPIO_reset(GPIOB);
    DL_UART_Main_reset(UART_0_INST);
    DL_UART_Main_reset(UART_1_INST);
    DL_TimerG_reset(TIMER_0_INST);

    DL_GPIO_enablePower(GPIOA);
    DL_GPIO_enablePower(GPIOB);
    DL_UART_Main_enablePower(UART_0_INST);
    DL_UART_Main_enablePower(UART_1_INST);
    DL_TimerG_enablePower(TIMER_0_INST);
    
    delay_cycles(POWER_STARTUP_DELAY);
}

/**
 * @brief GPIO初始化
 */
void SYSCFG_DL_GPIO_init(void)
{
    // LED GPIO配置
    DL_GPIO_initDigitalOutput(GPIO_LEDS_USER_LED_1_IOMUX);
    DL_GPIO_clearPins(GPIO_LEDS_PORT, GPIO_LEDS_USER_LED_1_PIN);
    DL_GPIO_enableOutput(GPIO_LEDS_PORT, GPIO_LEDS_USER_LED_1_PIN);

    // 按键GPIO配置
    DL_GPIO_initDigitalInputFeatures(GPIO_SWITCHES_USER_SWITCH_1_IOMUX,
        DL_GPIO_INVERSION_DISABLE, DL_GPIO_RESISTOR_PULL_UP,
        DL_GPIO_HYSTERESIS_DISABLE, DL_GPIO_WAKEUP_DISABLE);

    // UART0 GPIO配置
    DL_GPIO_initPeripheralOutputFunction(
        GPIO_UART_0_TX_IOMUX, DL_GPIO_PERIPHERAL_MUXING_UART_TX);
    DL_GPIO_initPeripheralInputFunction(
        GPIO_UART_0_RX_IOMUX, DL_GPIO_PERIPHERAL_MUXING_UART_RX);

    // UART1 GPIO配置
    DL_GPIO_initPeripheralOutputFunction(
        GPIO_UART_1_TX_IOMUX, DL_GPIO_PERIPHERAL_MUXING_UART_TX);
    DL_GPIO_initPeripheralInputFunction(
        GPIO_UART_1_RX_IOMUX, DL_GPIO_PERIPHERAL_MUXING_UART_RX);
}

/**
 * @brief 系统时钟初始化
 */
void SYSCFG_DL_SYSCTL_init(void)
{
    DL_SYSCTL_setBORThreshold(DL_SYSCTL_BOR_THRESHOLD_LEVEL_0);
    DL_SYSCTL_setFlashWaitState(DL_SYSCTL_FLASH_WAIT_STATE_2);

    DL_SYSCTL_setSYSOSCFreq(DL_SYSCTL_SYSOSC_FREQ_BASE);
    DL_SYSCTL_enableHFXT();
    delay_cycles(26);

    DL_SYSCTL_configHFCLK_HFXT(DL_SYSCTL_HFCLK_DIVIDER_1);
}

/**
 * @brief UART0初始化
 */
void SYSCFG_DL_UART_0_init(void)
{
    DL_UART_Main_setClockConfig(UART_0_INST, (DL_UART_Main_ClockConfig *) &gUART_0ClockConfig);

    DL_UART_Main_init(UART_0_INST, (DL_UART_Main_Config *) &gUART_0Config);
    DL_UART_Main_configInterrupts(UART_0_INST, DL_UART_MAIN_INTERRUPT_RX);

    DL_UART_Main_enable(UART_0_INST);
}

/**
 * @brief UART1初始化
 */
void SYSCFG_DL_UART_1_init(void)
{
    DL_UART_Main_setClockConfig(UART_1_INST, (DL_UART_Main_ClockConfig *) &gUART_1ClockConfig);

    DL_UART_Main_init(UART_1_INST, (DL_UART_Main_Config *) &gUART_1Config);
    
    DL_UART_Main_enable(UART_1_INST);
}

/**
 * @brief Timer0初始化 - 1ms定时器
 */
void SYSCFG_DL_TIMER_0_init(void)
{
    DL_TimerG_setClockConfig(TIMER_0_INST, (DL_TimerG_ClockConfig *) &gTIMER_0ClockConfig);

    DL_TimerG_initTimerMode(TIMER_0_INST, (DL_TimerG_TimerConfig *) &gTIMER_0TimerConfig);
    DL_TimerG_enableInterrupts(TIMER_0_INST, DL_TIMER_INTERRUPT_ZERO_EVENT);

    DL_TimerG_enableClock(TIMER_0_INST);
}

// UART配置结构体
static const DL_UART_Main_ClockConfig gUART_0ClockConfig = {
    .clockSel    = DL_UART_MAIN_CLOCK_BUSCLK,
    .divideRatio = DL_UART_MAIN_CLOCK_DIVIDE_RATIO_1
};

static const DL_UART_Main_Config gUART_0Config = {
    .mode        = DL_UART_MAIN_MODE_NORMAL,
    .direction   = DL_UART_MAIN_DIRECTION_TX_RX,
    .flowControl = DL_UART_MAIN_FLOW_CONTROL_NONE,
    .parity      = DL_UART_MAIN_PARITY_NONE,
    .wordLength  = DL_UART_MAIN_WORD_LENGTH_8_BITS,
    .stopBits    = DL_UART_MAIN_STOP_BITS_ONE
};

static const DL_UART_Main_ClockConfig gUART_1ClockConfig = {
    .clockSel    = DL_UART_MAIN_CLOCK_BUSCLK,
    .divideRatio = DL_UART_MAIN_CLOCK_DIVIDE_RATIO_1
};

static const DL_UART_Main_Config gUART_1Config = {
    .mode        = DL_UART_MAIN_MODE_NORMAL,
    .direction   = DL_UART_MAIN_DIRECTION_TX_RX,
    .flowControl = DL_UART_MAIN_FLOW_CONTROL_NONE,
    .parity      = DL_UART_MAIN_PARITY_NONE,
    .wordLength  = DL_UART_MAIN_WORD_LENGTH_8_BITS,
    .stopBits    = DL_UART_MAIN_STOP_BITS_ONE
};

// Timer配置结构体
static const DL_TimerG_ClockConfig gTIMER_0ClockConfig = {
    .clockSel    = DL_TIMER_CLOCK_BUSCLK,
    .divideRatio = DL_TIMER_CLOCK_DIVIDE_1,
    .prescale    = 31U  // 32MHz / 32 = 1MHz
};

static const DL_TimerG_TimerConfig gTIMER_0TimerConfig = {
    .period      = 1000U,  // 1000us = 1ms
    .timerMode   = DL_TIMER_TIMER_MODE_PERIODIC,
    .startTimer  = DL_TIMER_STOP
};
