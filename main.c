/*
 * 立创天猛星MSPM0G3507开发板 - 双总线舵机控制示例
 * 开发板官网：www.lckfb.com
 * 文档网站：wiki.lckfb.com
 * 
 * 功能说明：
 * 1. 使用UART串口通信控制两个总线舵机
 * 2. 舵机1连接到UART0，舵机2连接到UART1
 * 3. 支持角度控制、速度控制、位置读取等功能
 * 4. 通过串口命令控制舵机运动
 */

#include "ti_msp_dl_config.h"
#include "board.h"
#include "servo_bus.h"
#include <stdio.h>
#include <string.h>

// 全局变量
uint8_t uart_rx_buffer[32];
uint8_t uart_rx_index = 0;
bool command_ready = false;

// 函数声明
void process_command(void);
void print_help(void);

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // 初始化总线舵机
    servo_bus_init();
    
    // 打印启动信息
    lc_printf("\r\n=== 立创天猛星MSPM0G3507 双总线舵机控制系统 ===\r\n");
    lc_printf("开发板：立创天猛星MSPM0G3507\r\n");
    lc_printf("功能：双总线舵机控制\r\n");
    lc_printf("舵机1: UART0 (PA10-TX, PA11-RX)\r\n");
    lc_printf("舵机2: UART1 (PA8-TX, PA9-RX)\r\n");
    lc_printf("波特率: 115200\r\n");
    print_help();
    
    // 舵机初始化位置
    servo_set_angle(SERVO_1, 90);  // 舵机1设置到90度
    servo_set_angle(SERVO_2, 90);  // 舵机2设置到90度
    delay_ms(1000);
    
    lc_printf("舵机初始化完成，当前位置：90度\r\n");
    
    while (1)
    {
        // 处理串口命令
        if (command_ready)
        {
            process_command();
            command_ready = false;
            uart_rx_index = 0;
            memset(uart_rx_buffer, 0, sizeof(uart_rx_buffer));
        }
        
        // 定期读取舵机位置并显示
        static uint32_t last_read_time = 0;
        if (get_systick_ms() - last_read_time > 2000)  // 每2秒读取一次
        {
            uint16_t pos1 = servo_read_position(SERVO_1);
            uint16_t pos2 = servo_read_position(SERVO_2);
            
            lc_printf("舵机位置 - 舵机1: %d度, 舵机2: %d度\r\n", pos1, pos2);
            last_read_time = get_systick_ms();
        }
        
        delay_ms(10);
    }
}

// 处理串口命令
void process_command(void)
{
    char *cmd = (char*)uart_rx_buffer;
    int servo_id, angle, speed;
    
    if (strncmp(cmd, "help", 4) == 0)
    {
        print_help();
    }
    else if (sscanf(cmd, "set %d %d", &servo_id, &angle) == 2)
    {
        if (servo_id >= 1 && servo_id <= 2 && angle >= 0 && angle <= 180)
        {
            servo_set_angle(servo_id, angle);
            lc_printf("设置舵机%d角度为%d度\r\n", servo_id, angle);
        }
        else
        {
            lc_printf("参数错误！舵机ID: 1-2, 角度: 0-180\r\n");
        }
    }
    else if (sscanf(cmd, "speed %d %d %d", &servo_id, &angle, &speed) == 3)
    {
        if (servo_id >= 1 && servo_id <= 2 && angle >= 0 && angle <= 180 && speed > 0 && speed <= 1000)
        {
            servo_set_angle_with_speed(servo_id, angle, speed);
            lc_printf("设置舵机%d以速度%d转到%d度\r\n", servo_id, speed, angle);
        }
        else
        {
            lc_printf("参数错误！舵机ID: 1-2, 角度: 0-180, 速度: 1-1000\r\n");
        }
    }
    else if (sscanf(cmd, "read %d", &servo_id) == 1)
    {
        if (servo_id >= 1 && servo_id <= 2)
        {
            uint16_t pos = servo_read_position(servo_id);
            lc_printf("舵机%d当前位置: %d度\r\n", servo_id, pos);
        }
        else
        {
            lc_printf("参数错误！舵机ID: 1-2\r\n");
        }
    }
    else if (strncmp(cmd, "demo", 4) == 0)
    {
        lc_printf("开始演示模式...\r\n");
        
        // 演示：两个舵机同步运动
        for (int i = 0; i < 3; i++)
        {
            lc_printf("演示 %d/3: 舵机同步运动\r\n", i + 1);
            
            servo_set_angle_with_speed(SERVO_1, 0, 500);
            servo_set_angle_with_speed(SERVO_2, 180, 500);
            delay_ms(2000);
            
            servo_set_angle_with_speed(SERVO_1, 180, 500);
            servo_set_angle_with_speed(SERVO_2, 0, 500);
            delay_ms(2000);
        }
        
        // 回到中间位置
        servo_set_angle(SERVO_1, 90);
        servo_set_angle(SERVO_2, 90);
        lc_printf("演示完成，舵机回到90度位置\r\n");
    }
    else if (strncmp(cmd, "stop", 4) == 0)
    {
        servo_stop(SERVO_1);
        servo_stop(SERVO_2);
        lc_printf("所有舵机停止运动\r\n");
    }
    else
    {
        lc_printf("未知命令: %s\r\n", cmd);
        lc_printf("输入 'help' 查看帮助\r\n");
    }
}

// 打印帮助信息
void print_help(void)
{
    lc_printf("\r\n=== 命令帮助 ===\r\n");
    lc_printf("set <舵机ID> <角度>        - 设置舵机角度 (舵机ID: 1-2, 角度: 0-180)\r\n");
    lc_printf("speed <舵机ID> <角度> <速度> - 以指定速度设置角度 (速度: 1-1000)\r\n");
    lc_printf("read <舵机ID>             - 读取舵机当前位置\r\n");
    lc_printf("demo                      - 运行演示程序\r\n");
    lc_printf("stop                      - 停止所有舵机\r\n");
    lc_printf("help                      - 显示此帮助\r\n");
    lc_printf("\r\n示例:\r\n");
    lc_printf("  set 1 90      - 设置舵机1到90度\r\n");
    lc_printf("  speed 2 45 300 - 舵机2以速度300转到45度\r\n");
    lc_printf("  read 1        - 读取舵机1位置\r\n");
    lc_printf("==================\r\n");
}

// UART中断处理函数
void UART_0_INST_IRQHandler(void)
{
    switch (DL_UART_Main_getPendingInterrupt(UART_0_INST))
    {
        case DL_UART_MAIN_IIDX_RX:
        {
            uint8_t received_char = DL_UART_Main_receiveData(UART_0_INST);
            
            if (received_char == '\r' || received_char == '\n')
            {
                if (uart_rx_index > 0)
                {
                    uart_rx_buffer[uart_rx_index] = '\0';
                    command_ready = true;
                }
            }
            else if (uart_rx_index < sizeof(uart_rx_buffer) - 1)
            {
                uart_rx_buffer[uart_rx_index++] = received_char;
                // 回显字符
                DL_UART_Main_transmitData(UART_0_INST, received_char);
            }
            break;
        }
        default:
            break;
    }
}
