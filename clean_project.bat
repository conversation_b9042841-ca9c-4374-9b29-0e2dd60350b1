@echo off
echo ========================================
echo     CCS项目清理工具
echo     立创天猛星MSPM0G3507舵机控制项目
echo ========================================
echo.

echo [1/5] 清理编译输出文件...
if exist Debug (
    rmdir /s /q Debug
    echo     - 删除 Debug 目录
)
if exist Release (
    rmdir /s /q Release
    echo     - 删除 Release 目录
)
if exist .launches (
    rmdir /s /q .launches
    echo     - 删除 .launches 目录
)

echo.
echo [2/5] 删除冲突的源文件...
if exist mg996r_example.c (
    del mg996r_example.c
    echo     - 删除 mg996r_example.c
)
if exist main.c (
    del main.c
    echo     - 删除 main.c
)

echo.
echo [3/5] 清理IDE配置文件...
if exist launch.json (
    del launch.json
    echo     - 删除 launch.json
)
if exist .vscode (
    rmdir /s /q .vscode
    echo     - 删除 .vscode 目录
)
if exist *.code-workspace (
    del *.code-workspace
    echo     - 删除 VS Code workspace 文件
)

echo.
echo [4/5] 清理临时文件...
if exist *.tmp (
    del *.tmp
    echo     - 删除临时文件
)
if exist *.bak (
    del *.bak
    echo     - 删除备份文件
)

echo.
echo [5/5] 验证项目文件...
echo 保留的文件:
if exist empty.c echo     ✓ empty.c
if exist empty.syscfg echo     ✓ empty.syscfg
if exist servo_bus.h echo     ✓ servo_bus.h
if exist servo_bus.c echo     ✓ servo_bus.c
if exist ti_msp_dl_config.h echo     ✓ ti_msp_dl_config.h
if exist ti_msp_dl_config.c echo     ✓ ti_msp_dl_config.c
if exist .ccsproject echo     ✓ .ccsproject
if exist .project echo     ✓ .project

echo.
echo ========================================
echo 项目清理完成！
echo.
echo 下一步操作:
echo 1. 在CCS中刷新项目 (F5)
echo 2. 清理项目 (Project → Clean)
echo 3. 重新编译 (Project → Build)
echo ========================================
echo.
pause
