@echo off
chcp 65001 >nul
echo ========================================
echo     项目文件检查和修复工具
echo ========================================
echo.

echo [检查] 扫描项目中的所有C文件...
echo.
echo 发现的C文件:
for %%f in (*.c) do (
    echo     - %%f
)

echo.
echo [检查] 扫描项目中的所有头文件...
echo.
echo 发现的头文件:
for %%f in (*.h) do (
    echo     - %%f
)

echo.
echo [分析] 检查可能的冲突文件...
echo.

set conflict_found=0

REM 检查是否有多个main函数文件
if exist main.c (
    echo ⚠️  发现冲突: main.c ^(应该删除^)
    set conflict_found=1
)

if exist mg996r_example.c (
    echo ⚠️  发现冲突: mg996r_example.c ^(应该删除^)
    set conflict_found=1
)

REM 检查其他可能的示例文件
for %%f in (
    example_*.c
    test_*.c
    demo_*.c
    sample_*.c
    tutorial_*.c
    bsp_*.c
) do (
    if exist %%f (
        echo ⚠️  发现冲突: %%f ^(应该删除^)
        set conflict_found=1
    )
)

if %conflict_found%==0 (
    echo ✓ 没有发现明显的冲突文件
)

echo.
echo [检查] 验证必需文件...
echo.

set missing_files=0

if not exist empty.c (
    echo ❌ 缺失: empty.c
    set missing_files=1
) else (
    echo ✓ empty.c 存在
)

if not exist servo_bus.c (
    echo ❌ 缺失: servo_bus.c
    set missing_files=1
) else (
    echo ✓ servo_bus.c 存在
)

if not exist servo_bus.h (
    echo ❌ 缺失: servo_bus.h
    set missing_files=1
) else (
    echo ✓ servo_bus.h 存在
)

if not exist ti_msp_dl_config.c (
    echo ❌ 缺失: ti_msp_dl_config.c
    set missing_files=1
) else (
    echo ✓ ti_msp_dl_config.c 存在
)

if not exist ti_msp_dl_config.h (
    echo ❌ 缺失: ti_msp_dl_config.h
    set missing_files=1
) else (
    echo ✓ ti_msp_dl_config.h 存在
)

if not exist empty.syscfg (
    echo ❌ 缺失: empty.syscfg
    set missing_files=1
) else (
    echo ✓ empty.syscfg 存在
)

echo.
echo ========================================
echo 检查结果汇总:
echo ========================================

if %conflict_found%==1 (
    echo ⚠️  发现冲突文件 - 需要清理
) else (
    echo ✓ 没有冲突文件
)

if %missing_files%==1 (
    echo ❌ 有文件缺失 - 需要补充
) else (
    echo ✓ 所有必需文件都存在
)

echo.
echo ========================================
echo 建议的操作:
echo ========================================

if %conflict_found%==1 (
    echo.
    echo 1. 运行 complete_cleanup.bat 清理冲突文件
    echo 2. 或者手动删除上面列出的冲突文件
)

if %missing_files%==1 (
    echo.
    echo 3. 从原始项目复制缺失的文件
    echo 4. 确保所有必需文件都在项目目录中
)

echo.
echo 5. 在CCS中刷新项目 ^(F5^)
echo 6. 清理项目 ^(Project → Clean^)
echo 7. 重新编译 ^(Project → Build^)

echo.
echo ========================================
echo.

if %conflict_found%==1 (
    echo.
    set /p choice="是否立即清理冲突文件? (y/n): "
    if /i "!choice!"=="y" (
        echo.
        echo 开始清理冲突文件...
        
        if exist main.c (
            del main.c
            echo ✓ 删除 main.c
        )
        
        if exist mg996r_example.c (
            del mg996r_example.c
            echo ✓ 删除 mg996r_example.c
        )
        
        for %%f in (
            example_*.c
            test_*.c
            demo_*.c
            sample_*.c
            tutorial_*.c
            bsp_*.c
        ) do (
            if exist %%f (
                del %%f
                echo ✓ 删除 %%f
            )
        )
        
        echo.
        echo ✓ 冲突文件清理完成！
        echo 现在可以在CCS中重新编译项目了。
    )
)

echo.
pause
