/*
 * TI MSPM0G3507 配置头文件
 * 此文件通常由SysConfig工具生成，这里提供基本配置
 */

#ifndef TI_MSP_DL_CONFIG_H_
#define TI_MSP_DL_CONFIG_H_

#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

// 系统配置
#define CPUCLK_FREQ                     32000000

// GPIO配置
#define GPIO_LEDS_PORT                  GPIOA
#define GPIO_LEDS_USER_LED_1_PIN        DL_GPIO_PIN_0
#define GPIO_LEDS_USER_LED_1_IOMUX      (IOMUX_PINCM1)

#define GPIO_SWITCHES_PORT              GPIOB
#define GPIO_SWITCHES_USER_SWITCH_1_PIN DL_GPIO_PIN_21
#define GPIO_SWITCHES_USER_SWITCH_1_IOMUX (IOMUX_PINCM22)

// UART配置
#define UART_0_INST                     UART0
#define UART_0_INST_IRQHandler          UART0_IRQHandler
#define UART_0_INST_INT_IRQN            UART0_INT_IRQn
#define UART_0_BAUD_RATE                115200
#define UART_0_IBRD_32_MHZ_115200_BAUD  17
#define UART_0_FBRD_32_MHZ_115200_BAUD  23

#define GPIO_UART_0_RX_PORT             GPIOA
#define GPIO_UART_0_TX_PORT             GPIOA
#define GPIO_UART_0_RX_PIN              DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN              DL_GPIO_PIN_10
#define GPIO_UART_0_RX_IOMUX            (IOMUX_PINCM12)
#define GPIO_UART_0_TX_IOMUX            (IOMUX_PINCM11)

#define UART_1_INST                     UART1
#define UART_1_INST_IRQHandler          UART1_IRQHandler
#define UART_1_INST_INT_IRQN            UART1_INT_IRQn
#define UART_1_BAUD_RATE                115200
#define UART_1_IBRD_32_MHZ_115200_BAUD  17
#define UART_1_FBRD_32_MHZ_115200_BAUD  23

#define GPIO_UART_1_RX_PORT             GPIOA
#define GPIO_UART_1_TX_PORT             GPIOA
#define GPIO_UART_1_RX_PIN              DL_GPIO_PIN_9
#define GPIO_UART_1_TX_PIN              DL_GPIO_PIN_8
#define GPIO_UART_1_RX_IOMUX            (IOMUX_PINCM10)
#define GPIO_UART_1_TX_IOMUX            (IOMUX_PINCM9)

// Timer配置
#define TIMER_0_INST                    TIMG0
#define TIMER_0_INST_IRQHandler         TIMG0_IRQHandler
#define TIMER_0_INST_INT_IRQN           TIMG0_INT_IRQn

// 函数声明
void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_UART_1_init(void);
void SYSCFG_DL_TIMER_0_init(void);

#endif /* TI_MSP_DL_CONFIG_H_ */
