/*
 * 总线舵机驱动头文件
 * 支持常见的总线舵机协议（如LX-16A、LX-224等）
 * 
 * 功能特性：
 * - 支持多个舵机控制
 * - 角度控制和速度控制
 * - 位置读取和状态查询
 * - 错误检测和处理
 */

#ifndef __SERVO_BUS_H__
#define __SERVO_BUS_H__

#include "board.h"
#include <stdint.h>
#include <stdbool.h>

// 舵机ID定义
#define SERVO_1         1
#define SERVO_2         2

// 舵机命令定义
#define SERVO_CMD_MOVE_TIME_WRITE       1   // 指定时间内转到指定位置
#define SERVO_CMD_MOVE_TIME_READ        2   // 读取指定时间内转到指定位置
#define SERVO_CMD_MOVE_TIME_WAIT_WRITE  7   // 指定时间内转到指定位置(等待)
#define SERVO_CMD_MOVE_TIME_WAIT_READ   8   // 读取指定时间内转到指定位置(等待)
#define SERVO_CMD_MOVE_START            11  // 开始运动
#define SERVO_CMD_MOVE_STOP             12  // 停止运动
#define SERVO_CMD_ID_WRITE              13  // 设置舵机ID
#define SERVO_CMD_ID_READ               14  // 读取舵机ID
#define SERVO_CMD_ANGLE_OFFSET_ADJUST   17  // 角度偏移调整
#define SERVO_CMD_ANGLE_OFFSET_WRITE    18  // 角度偏移写入
#define SERVO_CMD_ANGLE_OFFSET_READ     19  // 角度偏移读取
#define SERVO_CMD_ANGLE_LIMIT_WRITE     20  // 角度限制写入
#define SERVO_CMD_ANGLE_LIMIT_READ      21  // 角度限制读取
#define SERVO_CMD_VIN_LIMIT_WRITE       22  // 电压限制写入
#define SERVO_CMD_VIN_LIMIT_READ        23  // 电压限制读取
#define SERVO_CMD_TEMP_MAX_LIMIT_WRITE  24  // 最高温度限制写入
#define SERVO_CMD_TEMP_MAX_LIMIT_READ   25  // 最高温度限制读取
#define SERVO_CMD_TEMP_READ             26  // 温度读取
#define SERVO_CMD_VIN_READ              27  // 电压读取
#define SERVO_CMD_POS_READ              28  // 位置读取
#define SERVO_CMD_OR_MOTOR_MODE_WRITE   29  // 电机模式写入
#define SERVO_CMD_OR_MOTOR_MODE_READ    30  // 电机模式读取
#define SERVO_CMD_LOAD_OR_UNLOAD_WRITE  31  // 力矩开关写入
#define SERVO_CMD_LOAD_OR_UNLOAD_READ   32  // 力矩开关读取
#define SERVO_CMD_LED_CTRL_WRITE        33  // LED控制写入
#define SERVO_CMD_LED_CTRL_READ         34  // LED控制读取
#define SERVO_CMD_LED_ERROR_WRITE       35  // LED错误写入
#define SERVO_CMD_LED_ERROR_READ        36  // LED错误读取

// 舵机参数定义
#define SERVO_ANGLE_MIN         0       // 最小角度
#define SERVO_ANGLE_MAX         180     // 最大角度
#define SERVO_SPEED_MIN         1       // 最小速度
#define SERVO_SPEED_MAX         1000    // 最大速度
#define SERVO_DEFAULT_TIME      1000    // 默认运动时间(ms)

// 通信参数
#define SERVO_FRAME_HEADER      0x55    // 帧头
#define SERVO_BROADCAST_ID      0xFE    // 广播ID
#define SERVO_TIMEOUT_MS        100     // 超时时间

// 错误代码
typedef enum {
    SERVO_OK = 0,
    SERVO_ERROR_TIMEOUT,
    SERVO_ERROR_CHECKSUM,
    SERVO_ERROR_INVALID_ID,
    SERVO_ERROR_INVALID_PARAM,
    SERVO_ERROR_COMM_FAIL
} servo_error_t;

// 舵机状态结构体
typedef struct {
    uint8_t id;
    uint16_t position;      // 当前位置 (0-1000对应0-240度)
    uint16_t target;        // 目标位置
    uint16_t speed;         // 运动速度
    uint8_t temperature;    // 温度
    uint16_t voltage;       // 电压
    bool is_moving;         // 是否在运动
    bool load_status;       // 力矩状态
} servo_status_t;

// 函数声明

/**
 * @brief 初始化总线舵机系统
 * @return servo_error_t 错误代码
 */
servo_error_t servo_bus_init(void);

/**
 * @brief 设置舵机角度
 * @param id 舵机ID (1-2)
 * @param angle 目标角度 (0-180度)
 * @return servo_error_t 错误代码
 */
servo_error_t servo_set_angle(uint8_t id, uint16_t angle);

/**
 * @brief 设置舵机角度和运动时间
 * @param id 舵机ID (1-2)
 * @param angle 目标角度 (0-180度)
 * @param time 运动时间 (毫秒)
 * @return servo_error_t 错误代码
 */
servo_error_t servo_set_angle_with_time(uint8_t id, uint16_t angle, uint16_t time);

/**
 * @brief 设置舵机角度和运动速度
 * @param id 舵机ID (1-2)
 * @param angle 目标角度 (0-180度)
 * @param speed 运动速度 (1-1000)
 * @return servo_error_t 错误代码
 */
servo_error_t servo_set_angle_with_speed(uint8_t id, uint16_t angle, uint16_t speed);

/**
 * @brief 读取舵机当前位置
 * @param id 舵机ID (1-2)
 * @return uint16_t 当前角度 (0-180度)，错误时返回0xFFFF
 */
uint16_t servo_read_position(uint8_t id);

/**
 * @brief 读取舵机状态
 * @param id 舵机ID (1-2)
 * @param status 状态结构体指针
 * @return servo_error_t 错误代码
 */
servo_error_t servo_read_status(uint8_t id, servo_status_t *status);

/**
 * @brief 停止舵机运动
 * @param id 舵机ID (1-2)
 * @return servo_error_t 错误代码
 */
servo_error_t servo_stop(uint8_t id);

/**
 * @brief 开始舵机运动
 * @param id 舵机ID (1-2)
 * @return servo_error_t 错误代码
 */
servo_error_t servo_start(uint8_t id);

/**
 * @brief 设置舵机力矩开关
 * @param id 舵机ID (1-2)
 * @param enable true-开启力矩，false-关闭力矩
 * @return servo_error_t 错误代码
 */
servo_error_t servo_set_torque(uint8_t id, bool enable);

/**
 * @brief 设置舵机LED状态
 * @param id 舵机ID (1-2)
 * @param enable true-开启LED，false-关闭LED
 * @return servo_error_t 错误代码
 */
servo_error_t servo_set_led(uint8_t id, bool enable);

/**
 * @brief 角度转换为舵机位置值
 * @param angle 角度 (0-180度)
 * @return uint16_t 位置值 (0-1000)
 */
uint16_t angle_to_position(uint16_t angle);

/**
 * @brief 舵机位置值转换为角度
 * @param position 位置值 (0-1000)
 * @return uint16_t 角度 (0-180度)
 */
uint16_t position_to_angle(uint16_t position);

/**
 * @brief 获取系统时间戳(毫秒)
 * @return uint32_t 时间戳
 */
uint32_t get_systick_ms(void);

/**
 * @brief 延时函数
 * @param ms 延时毫秒数
 */
void delay_ms(uint32_t ms);

#endif /* __SERVO_BUS_H__ */
