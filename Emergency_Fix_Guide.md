# 🚨 CCS编译错误紧急修复指南

## 当前问题
您的项目中有大量符号重定义错误，这是因为项目中混合了多个示例文件导致的。

## 🔥 立即执行的解决方案

### 方案1: 自动清理（推荐）
1. **关闭CCS**
2. **双击运行 `complete_cleanup.bat`**
3. **重新打开CCS**
4. **导入项目**

### 方案2: 手动清理
如果自动脚本无法运行，请手动执行以下步骤：

#### 步骤1: 关闭CCS
完全关闭Code Composer Studio

#### 步骤2: 删除所有冲突文件
在项目目录中删除以下文件（如果存在）：
```
❌ 删除这些文件:
├── mg996r_example.c
├── main.c  
├── bsp_*.c (所有bsp开头的C文件)
├── example_*.c (所有example开头的C文件)
├── test_*.c (所有test开头的C文件)
├── demo_*.c (所有demo开头的C文件)
├── sample_*.c (所有sample开头的C文件)
├── tutorial_*.c (所有tutorial开头的C文件)
├── bsp_*.h (所有bsp开头的头文件)
├── example_*.h (所有example开头的头文件)
├── test_*.h (所有test开头的头文件)
├── demo_*.h (所有demo开头的头文件)
├── sample_*.h (所有sample开头的头文件)
├── tutorial_*.h (所有tutorial开头的头文件)
```

#### 步骤3: 删除编译输出
删除以下目录：
```
├── Debug/
├── Release/
├── .launches/
├── .metadata/
├── .settings/
```

#### 步骤4: 删除IDE配置文件
```
├── launch.json
├── .vscode/
├── *.code-workspace
```

#### 步骤5: 确保只保留这些文件
```
✅ 保留这些文件:
├── empty.c              # 主程序文件
├── empty.syscfg         # SysConfig配置文件  
├── servo_bus.h          # 舵机驱动头文件
├── servo_bus.c          # 舵机驱动实现文件
├── ti_msp_dl_config.h   # TI配置头文件
├── ti_msp_dl_config.c   # TI配置实现文件
├── .ccsproject          # CCS项目配置
├── .project             # Eclipse项目配置
```

## 🛠️ 重新配置CCS项目

### 1. 重新创建项目配置文件

#### 创建 .ccsproject 文件
```xml
<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
    <ccsVersion value="12.0.0"/>
    <deviceVariant value="MSPM0G3507"/>
    <deviceFamily value="MSP"/>
    <deviceEndianness value="little"/>
    <codegenToolVersion value="20.2.7.LTS"/>
    <isElfFormat value="true"/>
    <connection value="common/targetdb/connections/TIXDS110_Connection.xml"/>
    <rts value="libc.a"/>
    <createSlaveProjects value=""/>
    <templateProperties value="id=empty_project_with_main.projectspec.empty_project_with_main"/>
    <isTargetManual value="false"/>
    <origin value="${MSPM0_SDK_INSTALL_DIR}/examples/nortos/LP_MSPM0G3507/empty"/>
</projectOptions>
```

#### 创建 .project 文件
```xml
<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
    <name>servo_control_mspm0g3507</name>
    <comment></comment>
    <projects>
    </projects>
    <buildSpec>
        <buildCommand>
            <name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
            <triggers>clean,full,incremental,</triggers>
            <arguments>
            </arguments>
        </buildCommand>
        <buildCommand>
            <name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
            <triggers>full,incremental,</triggers>
            <arguments>
            </arguments>
        </buildCommand>
    </buildSpec>
    <natures>
        <nature>com.ti.ccstudio.core.ccsNature</nature>
        <nature>org.eclipse.cdt.core.cnature</nature>
        <nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
        <nature>org.eclipse.cdt.core.ccnature</nature>
        <nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
    </natures>
</projectDescription>
```

### 2. 重新导入项目到CCS
1. **启动CCS**
2. **File → Import**
3. **General → Existing Projects into Workspace**
4. **选择项目目录**
5. **点击 Finish**

### 3. 配置项目属性
1. **右键项目 → Properties**
2. **Build → Arm Compiler → Include Options**
3. **添加包含路径**：
   ```
   "${MSPM0_SDK_INSTALL_DIR}/source"
   "${MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"
   "${MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib/m0p"
   "${MSPM0_SDK_INSTALL_DIR}/source/ti/devices/msp/m0p"
   "${MSPM0_SDK_INSTALL_DIR}/source/ti/devices/msp/m0p/mspm0g350x"
   ```

4. **Build → Arm Linker → File Search Path**
5. **添加库文件**：
   ```
   "${MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib/lib/ccs/m0p/mspm0g1x0x_g3x0x/driverlib.a"
   ```

## 🔍 验证修复

### 1. 检查文件
运行 `check_and_fix.bat` 来验证项目文件是否正确

### 2. 编译测试
1. **Project → Clean**
2. **Project → Build Project**
3. **检查编译输出**

### 3. 预期结果
编译成功后应该看到：
```
**** Build Finished ****
```

## 🚨 如果问题仍然存在

### 最后的解决方案：完全重建项目
1. **创建新的CCS项目**：
   - File → New → CCS Project
   - Target: MSPM0G3507
   - Template: Empty Project (with main.c)

2. **复制源文件**：
   - 只复制必需的6个文件
   - 删除CCS生成的默认main.c

3. **重新配置SysConfig**：
   - 双击empty.syscfg
   - 验证配置正确

## 📋 常见错误类型和解决方法

### 错误类型1: 符号重定义
```
symbol "XXX_IRQHandler" redefined
```
**解决**: 删除重复的中断处理函数定义

### 错误类型2: 多个main函数
```
multiple definition of 'main'
```
**解决**: 确保只有一个包含main函数的文件

### 错误类型3: 头文件冲突
```
conflicting types for 'function_name'
```
**解决**: 删除重复的头文件或函数声明

## 📞 紧急联系

如果以上方法都无法解决问题：
1. **备份当前项目**
2. **使用我提供的clean项目文件重新开始**
3. **逐个添加功能进行测试**

记住：**简单干净的项目结构是成功的关键！**
