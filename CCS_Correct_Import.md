# CCS正确导入项目的方法

## 问题说明
直接在CCS中打开文件夹会导致配置文件混乱，出现`launch.json`等非CCS标准文件。

## 正确的操作步骤

### 方法1: 创建新的CCS项目（推荐）

#### 1. 创建空项目
1. 打开CCS
2. File → New → CCS Project
3. 配置项目参数：
   - **Target**: MSPM0G3507
   - **Template**: Empty Project (with main.c)
   - **Project name**: servo_control_mspm0g3507
   - **Location**: 选择一个新的空目录（不要选择现有的Rily目录）

#### 2. 复制源文件
1. 将以下文件从Rily目录复制到新创建的CCS项目目录：
   ```
   empty.c
   empty.syscfg
   servo_bus.h
   servo_bus.c
   ti_msp_dl_config.h
   ti_msp_dl_config.c
   ```

2. 删除CCS自动生成的main.c文件

#### 3. 刷新项目
1. 在CCS中右键项目
2. 选择 "Refresh"
3. 确认所有文件都显示在项目中

### 方法2: 导入现有项目

#### 1. 清理Rily目录
首先清理可能存在的配置文件：
```bash
# 删除VS Code配置（如果存在）
删除 .vscode/ 目录
删除 launch.json 文件

# 删除其他IDE配置文件
删除 .settings/ 目录（如果不是CCS生成的）
删除 .project 文件（如果不是CCS生成的）
删除 .cproject 文件（如果不是CCS生成的）
```

#### 2. 创建CCS项目文件
在Rily目录中创建 `.project` 文件：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
    <name>servo_control_mspm0g3507</name>
    <comment></comment>
    <projects>
    </projects>
    <buildSpec>
        <buildCommand>
            <name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
            <triggers>clean,full,incremental,</triggers>
            <arguments>
            </arguments>
        </buildCommand>
        <buildCommand>
            <name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
            <triggers>full,incremental,</triggers>
            <arguments>
            </arguments>
        </buildCommand>
    </buildSpec>
    <natures>
        <nature>com.ti.ccstudio.core.ccsNature</nature>
        <nature>org.eclipse.cdt.core.cnature</nature>
        <nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
        <nature>org.eclipse.cdt.core.ccnature</nature>
        <nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
    </natures>
</projectDescription>
```

#### 3. 导入到CCS
1. 在CCS中：File → Import
2. 选择 "General" → "Existing Projects into Workspace"
3. 选择Rily目录
4. 点击 "Finish"

### 方法3: 使用CCS项目模板

#### 1. 创建项目模板文件
创建 `.ccsproject` 文件：

```xml
<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
    <ccsVersion value="12.0.0"/>
    <deviceVariant value="MSPM0G3507"/>
    <deviceFamily value="MSP"/>
    <deviceEndianness value="little"/>
    <codegenToolVersion value="20.2.7.LTS"/>
    <isElfFormat value="true"/>
    <connection value="common/targetdb/connections/TIXDS110_Connection.xml"/>
    <rts value="libc.a"/>
    <createSlaveProjects value=""/>
    <templateProperties value="id=empty_project_with_main.projectspec.empty_project_with_main"/>
    <isTargetManual value="false"/>
</projectOptions>
```

## 配置项目属性

### 1. 设置包含路径
右键项目 → Properties → Build → Arm Compiler → Include Options
添加路径：
```
"${MSPM0_SDK_INSTALL_DIR}/source"
"${MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"
"${MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib/m0p"
"${MSPM0_SDK_INSTALL_DIR}/source/ti/devices/msp/m0p"
"${MSPM0_SDK_INSTALL_DIR}/source/ti/devices/msp/m0p/mspm0g350x"
"${PROJECT_ROOT}"
```

### 2. 设置库文件
Build → Arm Linker → File Search Path
添加库：
```
"${MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib/lib/ccs/m0p/mspm0g1x0x_g3x0x/driverlib.a"
```

### 3. 设置预定义宏
Build → Arm Compiler → Predefined Symbols
添加：
```
__MSPM0G3507__
```

## 验证项目配置

### 1. 检查文件结构
确保项目包含以下文件：
```
servo_control_mspm0g3507/
├── .ccsproject
├── .project
├── .settings/
├── empty.c
├── empty.syscfg
├── servo_bus.h
├── servo_bus.c
├── ti_msp_dl_config.h
├── ti_msp_dl_config.c
└── targetConfigs/
```

### 2. 编译测试
1. Project → Clean Project
2. Project → Build Project
3. 检查编译输出，确保无错误

### 3. SysConfig验证
1. 双击 `empty.syscfg`
2. 确认SysConfig工具正常打开
3. 验证配置正确

## 避免launch.json问题的注意事项

### 1. 不要混用IDE
- 不要在同一目录中同时使用VS Code和CCS
- 如果需要使用VS Code查看代码，创建单独的副本

### 2. 正确的调试方式
- 使用CCS内置调试器：Run → Debug
- 不要尝试使用外部调试配置

### 3. 项目管理
- 始终通过CCS的项目管理功能操作
- 不要直接在文件系统中修改项目结构

## 如果仍然出现launch.json

### 删除不需要的文件
```bash
# 在项目目录中删除
rm -f launch.json
rm -rf .vscode/
rm -f *.code-workspace
```

### 重新导入项目
1. 在CCS中关闭项目
2. File → Import → Existing Projects into Workspace
3. 重新选择项目目录

## 总结

正确的做法是：
1. ✅ 使用CCS创建新项目
2. ✅ 复制源文件到CCS项目目录
3. ✅ 通过CCS项目管理功能操作
4. ❌ 不要直接打开文件夹
5. ❌ 不要混用不同的IDE配置
