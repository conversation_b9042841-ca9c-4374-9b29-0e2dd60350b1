# CCS项目配置详细步骤

## 1. 安装必需软件

### 1.1 安装CCS
1. 访问 https://www.ti.com/tool/CCSTUDIO
2. 下载CCS 12.0或更高版本
3. 安装时选择包含MSPM0系列支持

### 1.2 安装MSPM0 SDK
1. 访问 https://www.ti.com/tool/MSPM0-SDK
2. 下载MSPM0 SDK 2.01.00.03或更高版本
3. 安装到默认路径（通常是C:\ti\mspm0_sdk_2_01_00_03）

## 2. 创建CCS项目

### 2.1 新建项目
1. 打开CCS
2. File → New → CCS Project
3. 在Target选择页面：
   - Target: MSPM0G3507
   - Connection: Texas Instruments XDS110 USB Debug Probe
4. 在Project templates页面：
   - 选择 "Empty Project (with main.c)"
5. 项目名称: `servo_control_mspm0g3507`
6. 点击Finish

### 2.2 项目结构
创建后的项目结构应该是：
```
servo_control_mspm0g3507/
├── .ccsproject
├── .project
├── .settings/
├── Debug/
├── main.c
└── targetConfigs/
```

## 3. 配置项目属性

### 3.1 编译器设置
1. 右键项目 → Properties
2. Build → Arm Compiler → Include Options
3. 添加包含路径：
   ```
   "${MSPM0_SDK_INSTALL_DIR}/source"
   "${MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib"
   "${MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib/m0p"
   "${MSPM0_SDK_INSTALL_DIR}/source/ti/devices/msp/m0p"
   "${MSPM0_SDK_INSTALL_DIR}/source/ti/devices/msp/m0p/mspm0g350x"
   ```

### 3.2 链接器设置
1. Build → Arm Linker → File Search Path
2. 添加库文件路径：
   ```
   "${MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib/lib/ccs/m0p/mspm0g1x0x_g3x0x/driverlib.a"
   ```

### 3.3 预定义宏
1. Build → Arm Compiler → Predefined Symbols
2. 添加预定义宏：
   ```
   __MSPM0G3507__
   ```

## 4. 添加项目文件

### 4.1 删除默认main.c
1. 删除CCS自动生成的main.c文件

### 4.2 添加项目源文件
将以下文件复制到项目根目录：
- `empty.c` (主程序文件)
- `empty.syscfg` (SysConfig配置文件)
- `servo_bus.h` (舵机驱动头文件)
- `servo_bus.c` (舵机驱动实现)
- `ti_msp_dl_config.h` (配置头文件)
- `ti_msp_dl_config.c` (配置实现)

### 4.3 刷新项目
1. 右键项目 → Refresh
2. 确认所有文件都显示在项目中

## 5. SysConfig配置

### 5.1 打开SysConfig
1. 双击 `empty.syscfg` 文件
2. SysConfig工具会自动打开

### 5.2 验证配置
确认以下配置正确：

#### GPIO配置
- LED: PA0 (输出)
- 按键: PB21 (输入，上拉)

#### UART配置
- UART0: PA10(TX), PA11(RX), 115200波特率
- UART1: PA8(TX), PA9(RX), 115200波特率

#### Timer配置
- Timer0: 1ms周期定时器，开启中断

### 5.3 生成代码
1. 在SysConfig中点击 "Generate" 按钮
2. 或者保存文件时自动生成

## 6. 编译项目

### 6.1 编译设置
1. 确保项目配置为Debug模式
2. Project → Build Configurations → Set Active → Debug

### 6.2 开始编译
1. 选中项目
2. Project → Build Project (Ctrl+B)
3. 查看Console输出，确保编译成功

### 6.3 编译输出
成功编译后会生成：
- `Debug/servo_control_mspm0g3507.out` (可执行文件)
- `Debug/servo_control_mspm0g3507.map` (内存映射文件)

## 7. 硬件连接

### 7.1 开发板连接
1. 使用USB线连接开发板到电脑
2. 确保DAP-LINK驱动已安装

### 7.2 舵机连接
```
舵机1 (LX-16A):
- 红线(VCC) → 5V电源
- 黑线(GND) → GND
- 黄线(信号) → PA10(TX) 和 PA11(RX)

舵机2 (LX-16A):
- 红线(VCC) → 5V电源  
- 黑线(GND) → GND
- 黄线(信号) → PA8(TX) 和 PA9(RX)
```

### 7.3 串口连接
```
USB转串口模块:
- TX → PA11 (开发板RX)
- RX → PA10 (开发板TX)
- GND → GND
```

## 8. 下载和调试

### 8.1 配置调试器
1. Run → Debug Configurations
2. 选择 "Code Composer Studio - Device Debugging"
3. 创建新配置：
   - Name: servo_control_debug
   - Project: servo_control_mspm0g3507
   - Program: Debug/servo_control_mspm0g3507.out
   - Device: MSPM0G3507

### 8.2 下载程序
1. 点击Debug按钮 (F11)
2. CCS会自动下载程序到开发板
3. 程序会在main函数入口处暂停

### 8.3 运行程序
1. 点击Resume按钮 (F8) 或按F8键
2. 程序开始运行

## 9. 测试验证

### 9.1 串口测试
1. 打开串口调试助手
2. 设置波特率115200，8N1
3. 连接到对应COM口
4. 复位开发板，应该看到启动信息

### 9.2 舵机测试
1. 在串口中输入命令：`set 1 90`
2. 舵机1应该转到90度位置
3. 输入 `read 1` 读取位置反馈

### 9.3 演示模式
1. 输入 `demo` 命令
2. 观察两个舵机的同步运动

## 10. 常见问题解决

### 10.1 编译错误
**问题**: 找不到头文件
**解决**: 检查Include路径配置，确保SDK路径正确

**问题**: 链接错误
**解决**: 检查库文件路径，确保driverlib.a文件存在

### 10.2 下载错误
**问题**: 无法连接到目标设备
**解决**: 
1. 检查USB连接
2. 重新安装DAP-LINK驱动
3. 尝试不同的USB端口

### 10.3 运行错误
**问题**: 程序运行异常
**解决**:
1. 检查时钟配置
2. 验证GPIO配置
3. 使用调试器单步执行

### 10.4 舵机无响应
**问题**: 舵机不动作
**解决**:
1. 检查舵机电源（5V）
2. 验证接线连接
3. 检查舵机ID设置
4. 使用示波器检查信号

## 11. 进阶配置

### 11.1 优化编译
1. 切换到Release模式
2. 启用编译器优化
3. 减小代码体积

### 11.2 添加断言
1. 启用断言检查
2. 添加错误处理代码

### 11.3 性能分析
1. 使用CCS性能分析工具
2. 优化关键代码路径

---

按照以上步骤，您应该能够成功创建和配置CCS项目，编译并下载程序到立创天猛星MSPM0G3507开发板。
