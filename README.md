# 立创天猛星MSPM0G3507 双总线舵机控制系统

## 项目简介

本项目基于立创天猛星MSPM0G3507开发板，实现了对两个总线舵机的精确控制。支持角度控制、速度控制、位置读取等功能，并提供了完整的串口命令控制接口。

## 硬件配置

### 开发板
- **型号**: 立创天猛星MSPM0G3507
- **主控**: TI MSPM0G3507微控制器
- **时钟**: 32MHz
- **电源**: 3.3V/5V

### 舵机连接
- **舵机1**: 连接到UART0 (PA10-TX, PA11-RX)
- **舵机2**: 连接到UART1 (PA8-TX, PA9-RX)
- **波特率**: 115200
- **协议**: 支持LX-16A、LX-224等总线舵机

### 引脚定义
```
舵机1 (UART0):
- TX: PA10
- RX: PA11

舵机2 (UART1):
- TX: PA8
- RX: PA9

调试串口 (UART0):
- TX: PA10
- RX: PA11

LED: PA0
按键: PB21
```

## 功能特性

### 基本功能
- ✅ 双舵机独立控制
- ✅ 角度控制 (0-180度)
- ✅ 速度控制 (1-1000)
- ✅ 位置读取
- ✅ 运动状态监控
- ✅ 串口命令控制

### 高级功能
- ✅ 力矩开关控制
- ✅ LED状态控制
- ✅ 错误检测和处理
- ✅ 演示模式
- ✅ 实时状态显示

## 软件架构

```
main.c              # 主程序和命令处理
├── servo_bus.h     # 总线舵机驱动头文件
├── servo_bus.c     # 总线舵机驱动实现
├── board.h         # 硬件抽象层头文件
├── board.c         # 硬件抽象层实现
├── ti_msp_dl_config.h  # TI配置头文件
└── ti_msp_dl_config.c  # TI配置实现
```

## 使用说明

### 编译环境
- **IDE**: Keil MDK-ARM v5.38a 或更高版本
- **编译器**: Arm Clang v6.16 或更高版本
- **SDK**: MSPM0 SDK
- **配置工具**: SysConfig 1.21.x

### 串口命令

连接开发板后，通过串口终端发送以下命令：

#### 基本控制命令
```bash
# 设置舵机角度
set <舵机ID> <角度>
示例: set 1 90        # 设置舵机1到90度

# 设置舵机角度和速度
speed <舵机ID> <角度> <速度>
示例: speed 2 45 300  # 舵机2以速度300转到45度

# 读取舵机位置
read <舵机ID>
示例: read 1          # 读取舵机1当前位置
```

#### 系统命令
```bash
demo                  # 运行演示程序
stop                  # 停止所有舵机
help                  # 显示帮助信息
```

### 参数范围
- **舵机ID**: 1-2
- **角度**: 0-180度
- **速度**: 1-1000

## 代码示例

### 基本使用
```c
#include "servo_bus.h"

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    servo_bus_init();
    
    // 设置舵机角度
    servo_set_angle(SERVO_1, 90);
    servo_set_angle(SERVO_2, 45);
    
    // 读取位置
    uint16_t pos1 = servo_read_position(SERVO_1);
    uint16_t pos2 = servo_read_position(SERVO_2);
    
    while(1)
    {
        // 主循环
    }
}
```

### 高级控制
```c
// 设置舵机以指定速度运动
servo_set_angle_with_speed(SERVO_1, 180, 500);

// 控制力矩开关
servo_set_torque(SERVO_1, true);   // 开启力矩
servo_set_torque(SERVO_2, false);  // 关闭力矩

// 控制LED
servo_set_led(SERVO_1, true);      // 开启LED
```

## 协议说明

### 总线舵机通信协议
```
发送格式: 0x55 0x55 ID LEN CMD PARAM1 PARAM2 ... CHECKSUM
接收格式: 0x55 0x55 ID LEN CMD PARAM1 PARAM2 ... CHECKSUM

其中:
- 0x55 0x55: 帧头
- ID: 舵机ID (1-254)
- LEN: 数据长度
- CMD: 命令码
- PARAM: 参数
- CHECKSUM: 校验和 (~(ID+LEN+CMD+PARAM...))
```

### 主要命令码
- `0x01`: 指定时间内转到指定位置
- `0x1C`: 读取当前位置
- `0x0C`: 停止运动
- `0x0B`: 开始运动
- `0x1F`: 力矩开关控制
- `0x21`: LED控制

## 故障排除

### 常见问题

1. **舵机无响应**
   - 检查接线是否正确
   - 确认波特率设置为115200
   - 检查舵机电源供电

2. **位置读取失败**
   - 确认舵机支持位置反馈
   - 检查通信线路
   - 增加通信延时

3. **编译错误**
   - 确认使用正确的Keil版本
   - 检查MSPM0 SDK安装
   - 验证头文件路径

### 调试技巧
- 使用串口监控通信数据
- 检查系统时钟配置
- 验证GPIO引脚配置

## 扩展功能

### 支持更多舵机
可以通过添加更多UART接口支持更多舵机，或使用RS485总线连接多个舵机。

### 添加传感器
可以集成编码器、IMU等传感器实现闭环控制。

### 无线控制
可以添加WiFi、蓝牙模块实现无线控制。

## 技术支持

- **开发板官网**: www.lckfb.com
- **文档网站**: wiki.lckfb.com
- **技术论坛**: https://www.jlc-bbs.com/lckfb
- **视频教程**: 【立创开发板】bilibili账号

## 许可证

本项目采用开源许可证，详情请参考立创开发板开源协议。

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持双舵机控制
- 实现基本串口命令
- 添加演示模式
