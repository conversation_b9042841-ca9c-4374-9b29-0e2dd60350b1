@echo off
chcp 65001 >nul
echo ========================================
echo     CCS项目完全清理工具
echo     立创天猛星MSPM0G3507舵机控制项目
echo ========================================
echo.

echo [1/8] 停止CCS进程（如果正在运行）...
taskkill /f /im ccstudio.exe 2>nul
timeout /t 2 >nul

echo.
echo [2/8] 删除所有编译输出和缓存...
if exist Debug (
    rmdir /s /q Debug
    echo     ✓ 删除 Debug 目录
)
if exist Release (
    rmdir /s /q Release  
    echo     ✓ 删除 Release 目录
)
if exist .launches (
    rmdir /s /q .launches
    echo     ✓ 删除 .launches 目录
)
if exist .metadata (
    rmdir /s /q .metadata
    echo     ✓ 删除 .metadata 目录
)
if exist .settings (
    rmdir /s /q .settings
    echo     ✓ 删除 .settings 目录
)

echo.
echo [3/8] 删除所有冲突的源文件...
for %%f in (
    mg996r_example.c
    main.c
    bsp_*.c
    example_*.c
    test_*.c
    demo_*.c
    sample_*.c
    tutorial_*.c
) do (
    if exist %%f (
        del %%f
        echo     ✓ 删除 %%f
    )
)

echo.
echo [4/8] 删除所有冲突的头文件...
for %%f in (
    bsp_*.h
    example_*.h
    test_*.h
    demo_*.h
    sample_*.h
    tutorial_*.h
) do (
    if exist %%f (
        del %%f
        echo     ✓ 删除 %%f
    )
)

echo.
echo [5/8] 清理IDE配置文件...
if exist launch.json (
    del launch.json
    echo     ✓ 删除 launch.json
)
if exist .vscode (
    rmdir /s /q .vscode
    echo     ✓ 删除 .vscode 目录
)
for %%f in (*.code-workspace) do (
    if exist %%f (
        del %%f
        echo     ✓ 删除 %%f
    )
)

echo.
echo [6/8] 清理临时文件和备份文件...
for %%f in (
    *.tmp
    *.bak
    *.orig
    *.log
    *.out
    *.map
    *.obj
    *.o
    *.d
    *.pp
    *~
) do (
    if exist %%f (
        del %%f >nul 2>&1
    )
)
echo     ✓ 清理临时文件完成

echo.
echo [7/8] 重新创建必要的CCS配置文件...

echo 创建 .ccsproject 文件...
(
echo ^<?xml version="1.0" encoding="UTF-8" ?^>
echo ^<?ccsproject version="1.0"?^>
echo ^<projectOptions^>
echo     ^<ccsVersion value="12.0.0"/^>
echo     ^<deviceVariant value="MSPM0G3507"/^>
echo     ^<deviceFamily value="MSP"/^>
echo     ^<deviceEndianness value="little"/^>
echo     ^<codegenToolVersion value="20.2.7.LTS"/^>
echo     ^<isElfFormat value="true"/^>
echo     ^<connection value="common/targetdb/connections/TIXDS110_Connection.xml"/^>
echo     ^<rts value="libc.a"/^>
echo     ^<createSlaveProjects value=""/^>
echo     ^<templateProperties value="id=empty_project_with_main.projectspec.empty_project_with_main"/^>
echo     ^<isTargetManual value="false"/^>
echo     ^<origin value="${MSPM0_SDK_INSTALL_DIR}/examples/nortos/LP_MSPM0G3507/empty"/^>
echo ^</projectOptions^>
) > .ccsproject

echo 创建 .project 文件...
(
echo ^<?xml version="1.0" encoding="UTF-8"?^>
echo ^<projectDescription^>
echo     ^<name^>servo_control_mspm0g3507^</name^>
echo     ^<comment^>^</comment^>
echo     ^<projects^>
echo     ^</projects^>
echo     ^<buildSpec^>
echo         ^<buildCommand^>
echo             ^<name^>org.eclipse.cdt.managedbuilder.core.genmakebuilder^</name^>
echo             ^<triggers^>clean,full,incremental,^</triggers^>
echo             ^<arguments^>
echo             ^</arguments^>
echo         ^</buildCommand^>
echo         ^<buildCommand^>
echo             ^<name^>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder^</name^>
echo             ^<triggers^>full,incremental,^</triggers^>
echo             ^<arguments^>
echo             ^</arguments^>
echo         ^</buildCommand^>
echo     ^</buildSpec^>
echo     ^<natures^>
echo         ^<nature^>com.ti.ccstudio.core.ccsNature^</nature^>
echo         ^<nature^>org.eclipse.cdt.core.cnature^</nature^>
echo         ^<nature^>org.eclipse.cdt.managedbuilder.core.managedBuildNature^</nature^>
echo         ^<nature^>org.eclipse.cdt.core.ccnature^</nature^>
echo         ^<nature^>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature^</nature^>
echo     ^</natures^>
echo ^</projectDescription^>
) > .project

echo     ✓ CCS配置文件创建完成

echo.
echo [8/8] 验证项目文件完整性...
echo.
echo 必需文件检查:
if exist empty.c (
    echo     ✓ empty.c - 主程序文件
) else (
    echo     ✗ empty.c - 缺失！
)

if exist empty.syscfg (
    echo     ✓ empty.syscfg - SysConfig配置文件
) else (
    echo     ✗ empty.syscfg - 缺失！
)

if exist servo_bus.h (
    echo     ✓ servo_bus.h - 舵机驱动头文件
) else (
    echo     ✗ servo_bus.h - 缺失！
)

if exist servo_bus.c (
    echo     ✓ servo_bus.c - 舵机驱动实现文件
) else (
    echo     ✗ servo_bus.c - 缺失！
)

if exist ti_msp_dl_config.h (
    echo     ✓ ti_msp_dl_config.h - TI配置头文件
) else (
    echo     ✗ ti_msp_dl_config.h - 缺失！
)

if exist ti_msp_dl_config.c (
    echo     ✓ ti_msp_dl_config.c - TI配置实现文件
) else (
    echo     ✗ ti_msp_dl_config.c - 缺失！
)

if exist .ccsproject (
    echo     ✓ .ccsproject - CCS项目配置
) else (
    echo     ✗ .ccsproject - 缺失！
)

if exist .project (
    echo     ✓ .project - Eclipse项目配置
) else (
    echo     ✗ .project - 缺失！
)

echo.
echo 当前目录中的所有文件:
echo ----------------------------------------
dir /b *.c *.h *.syscfg 2>nul
echo ----------------------------------------

echo.
echo ========================================
echo 项目完全清理完成！
echo.
echo 下一步操作:
echo 1. 重新启动CCS
echo 2. 导入项目: File → Import → Existing Projects into Workspace
echo 3. 选择当前目录
echo 4. 刷新项目 (F5)
echo 5. 清理项目 (Project → Clean)
echo 6. 重新编译 (Project → Build)
echo ========================================
echo.
echo 如果仍有编译错误，请检查缺失的文件！
echo.
pause
