# 立创天猛星MSPM0G3507 双总线舵机控制系统 Makefile
# 适用于Keil MDK-ARM编译环境

# 项目名称
PROJECT_NAME = servo_control_mspm0g3507

# 目标芯片
TARGET_DEVICE = MSPM0G3507

# 编译器设置
CC = armclang
AS = armasm
LD = armlink
AR = armar

# 编译选项
CFLAGS = -c --target=arm-arm-none-eabi -mcpu=cortex-m0plus
CFLAGS += -O1 -g -fshort-enums -fshort-wchar
CFLAGS += -D__MICROLIB -DTARGET_IS_MSP432E4
CFLAGS += -I. -I./inc

# 汇编选项
ASFLAGS = --cpu Cortex-M0+ --pd "__MICROLIB SETA 1"

# 链接选项
LDFLAGS = --cpu Cortex-M0+ --library_type=microlib
LDFLAGS += --strict --scatter "$(PROJECT_NAME).sct"
LDFLAGS += --summary_stderr --info summarysizes --map
LDFLAGS += --load_addr_map_info --xref --callgraph --symbols
LDFLAGS += --info sizes --info totals --info unused --info veneers

# 源文件
SOURCES = main.c \
          servo_bus.c \
          board.c \
          ti_msp_dl_config.c

# 头文件目录
INCLUDES = -I. \
           -I./inc \
           -I./ti/driverlib \
           -I./ti/devices/msp/m0p/mspm0g350x

# 目标文件
OBJECTS = $(SOURCES:.c=.o)

# 默认目标
all: $(PROJECT_NAME).axf

# 编译规则
%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDES) -o $@ $<

# 链接规则
$(PROJECT_NAME).axf: $(OBJECTS)
	$(LD) $(LDFLAGS) -o $@ $^

# 清理
clean:
	del /Q *.o *.axf *.map *.lst *.htm

# 下载到开发板
download: $(PROJECT_NAME).axf
	@echo "请使用Keil MDK或UniFlash工具下载程序到开发板"

# 帮助信息
help:
	@echo "可用目标:"
	@echo "  all      - 编译项目"
	@echo "  clean    - 清理编译文件"
	@echo "  download - 下载程序到开发板"
	@echo "  help     - 显示此帮助信息"

.PHONY: all clean download help
