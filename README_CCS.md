# 立创天猛星MSPM0G3507 双总线舵机控制系统 - CCS版本

## 项目简介

本项目基于立创天猛星MSPM0G3507开发板，使用CCS（Code Composer Studio）开发环境，实现了对两个总线舵机的精确控制。

## 开发环境要求

### 必需软件
1. **CCS (Code Composer Studio)** - TI官方IDE
   - 版本：12.0或更高
   - 下载地址：https://www.ti.com/tool/CCSTUDIO

2. **MSPM0 SDK**
   - 版本：2.01.00.03或更高
   - 下载地址：https://www.ti.com/tool/MSPM0-SDK

3. **SysConfig工具**
   - 版本：1.18.0或更高
   - 通常随CCS一起安装

### 可选软件
- **UniFlash** - 用于程序下载和调试
- **串口调试助手** - 用于串口通信测试

## 硬件配置

### 开发板连接
```
立创天猛星MSPM0G3507开发板

舵机1连接 (UART0):
- TX: PA10 (引脚11)
- RX: PA11 (引脚12)

舵机2连接 (UART1):
- TX: PA8  (引脚9)
- RX: PA9  (引脚10)

调试接口:
- UART0同时用于调试输出
- 波特率: 115200

电源:
- VCC: 3.3V (开发板供电)
- 舵机电源: 5V (外部供电)
- GND: 共地
```

### 舵机支持
- **LX-16A** 总线舵机
- **LX-224** 总线舵机
- **其他兼容** 总线舵机协议的舵机

## CCS项目配置

### 1. 创建新项目
1. 打开CCS
2. File → New → CCS Project
3. 选择Target: MSPM0G3507
4. 选择Template: Empty Project (with main.c)
5. 项目名称: servo_control_mspm0g3507

### 2. 添加SDK支持
1. 右键项目 → Properties
2. Build → Arm Compiler → Include Options
3. 添加SDK路径:
   ```
   ${MSPM0_SDK_INSTALL_DIR}/source
   ${MSPM0_SDK_INSTALL_DIR}/source/ti/driverlib
   ```

### 3. 配置SysConfig
1. 将`empty.syscfg`文件添加到项目根目录
2. 双击打开SysConfig配置界面
3. 配置已包含:
   - GPIO (LED和按键)
   - UART0 (调试和舵机1)
   - UART1 (舵机2)
   - Timer0 (1ms定时器)

### 4. 添加源文件
将以下文件添加到项目:
```
empty.c          # 主程序文件
servo_bus.h      # 舵机驱动头文件
servo_bus.c      # 舵机驱动实现
ti_msp_dl_config.h  # 配置头文件
ti_msp_dl_config.c  # 配置实现
empty.syscfg     # SysConfig配置文件
```

## 编译和下载

### 编译项目
1. 在CCS中选择项目
2. Project → Build Project (Ctrl+B)
3. 检查编译输出，确保无错误

### 下载程序
#### 方法1: 使用CCS调试器
1. 连接DAP-LINK调试器到开发板
2. Run → Debug (F11)
3. 程序自动下载并开始调试

#### 方法2: 使用UniFlash
1. 打开UniFlash工具
2. 选择MSPM0G3507设备
3. 选择编译生成的.out文件
4. 点击Load Image下载

### 串口连接
1. 连接USB转串口模块到PA10(TX)和PA11(RX)
2. 打开串口调试助手
3. 设置波特率115200，8N1
4. 复位开发板，查看启动信息

## 使用说明

### 串口命令
连接串口后，可以发送以下命令：

```bash
# 基本控制
set 1 90          # 设置舵机1到90度
set 2 45          # 设置舵机2到45度

# 速度控制
speed 1 180 500   # 舵机1以速度500转到180度
speed 2 0 300     # 舵机2以速度300转到0度

# 状态读取
read 1            # 读取舵机1当前位置
read 2            # 读取舵机2当前位置

# 系统命令
demo              # 运行演示程序
stop              # 停止所有舵机
help              # 显示帮助信息
```

### 参数范围
- **舵机ID**: 1-2
- **角度**: 0-180度
- **速度**: 1-1000

## 代码结构

### 主要文件说明
```
empty.c           # 主程序，包含命令处理和中断函数
├── main()        # 主函数，系统初始化和主循环
├── process_command()  # 串口命令处理
├── print_help()  # 帮助信息显示
├── UART_0_INST_IRQHandler()  # UART中断处理
└── TIMER_0_INST_IRQHandler() # 定时器中断处理

servo_bus.c       # 舵机驱动实现
├── servo_bus_init()          # 舵机系统初始化
├── servo_set_angle()         # 设置舵机角度
├── servo_set_angle_with_speed() # 设置角度和速度
├── servo_read_position()     # 读取舵机位置
└── servo_stop()              # 停止舵机运动

ti_msp_dl_config.c # 硬件配置实现
├── SYSCFG_DL_init()          # 系统初始化
├── SYSCFG_DL_GPIO_init()     # GPIO初始化
├── SYSCFG_DL_UART_0_init()   # UART0初始化
└── SYSCFG_DL_UART_1_init()   # UART1初始化
```

### SysConfig配置
`empty.syscfg`文件包含了完整的硬件配置：
- **GPIO**: LED(PA0)和按键(PB21)
- **UART0**: 调试串口和舵机1通信
- **UART1**: 舵机2通信
- **Timer0**: 1ms系统定时器

## 调试技巧

### 常见问题解决
1. **编译错误**
   - 检查SDK路径配置
   - 确认所有源文件已添加到项目

2. **下载失败**
   - 检查DAP-LINK连接
   - 确认目标设备选择正确

3. **舵机无响应**
   - 检查接线是否正确
   - 确认舵机电源供电
   - 使用示波器检查通信信号

4. **串口无输出**
   - 检查串口连接
   - 确认波特率设置
   - 检查printf重定向配置

### 调试方法
1. **使用CCS调试器**
   - 设置断点调试程序流程
   - 查看变量值和寄存器状态

2. **串口调试**
   - 添加printf语句输出调试信息
   - 监控舵机通信数据

3. **逻辑分析仪**
   - 分析UART通信时序
   - 检查数据帧格式

## 扩展功能

### 添加更多舵机
可以通过以下方式扩展：
1. 使用RS485总线连接多个舵机
2. 添加更多UART接口
3. 使用I2C转UART模块

### 集成传感器
- MPU6050六轴传感器
- 编码器反馈
- 力传感器

### 无线控制
- ESP32 WiFi模块
- HC-05蓝牙模块
- LoRa无线模块

## 技术支持

- **立创开发板官网**: www.lckfb.com
- **技术文档**: wiki.lckfb.com
- **社区论坛**: https://www.jlc-bbs.com/lckfb
- **视频教程**: 搜索"立创开发板"

## 版本历史

### v1.0.0 (2024-01-01)
- 初始CCS版本发布
- 支持双舵机控制
- 完整的SysConfig配置
- 串口命令控制界面

---

**注意**: 本项目专为CCS开发环境优化，如需使用其他IDE，请参考相应的移植说明。
