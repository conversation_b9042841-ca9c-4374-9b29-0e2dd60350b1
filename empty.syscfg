/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.18.0+3266"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();
const UART2  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                          = "GPIO_LEDS";
GPIO1.associatedPins.create(1);
GPIO1.associatedPins[0].$name        = "USER_LED_1";
GPIO1.associatedPins[0].assignedPort = "PORTA";
GPIO1.associatedPins[0].assignedPin  = "0";
GPIO1.associatedPins[0].pin.$assign  = "PA0";

GPIO2.$name                          = "GPIO_SWITCHES";
GPIO2.associatedPins.create(1);
GPIO2.associatedPins[0].$name        = "USER_SWITCH_1";
GPIO2.associatedPins[0].direction    = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].assignedPort = "PORTB";
GPIO2.associatedPins[0].assignedPin  = "21";
GPIO2.associatedPins[0].pin.$assign  = "PB21";

SYSCTL.clockTreeEn = true;

TIMER1.$name              = "TIMER_0";
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerPeriod        = "1 ms";
TIMER1.interrupts         = ["ZERO"];
TIMER1.peripheral.$assign = "TIMG0";

UART1.$name                    = "UART_0";
UART1.targetBaudRate           = 115200;
UART1.enabledInterrupts        = ["RX"];
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";

UART2.$name                    = "UART_1";
UART2.targetBaudRate           = 115200;
UART2.peripheral.$assign       = "UART1";
UART2.peripheral.rxPin.$assign = "PA9";
UART2.peripheral.txPin.$assign = "PA8";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
TIMER1.peripheral.ccp0Pin.$suggestSolution = "PA12";
