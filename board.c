/*
 * 开发板硬件抽象层实现文件
 */

#include "board.h"
#include "servo_bus.h"
#include <stdarg.h>

// 全局变量
static volatile uint32_t system_tick = 0;

/**
 * @brief 板级初始化
 */
void board_init(void)
{
    // GPIO初始化已在ti_msp_dl_config.c中完成
    
    // 初始化LED为熄灭状态
    led_set(false);
    
    // 启动系统滴答定时器
    // 使用Timer0作为1ms定时器
    DL_TimerG_startCounter(TIMER_0_INST);
}

/**
 * @brief LED控制
 */
void led_set(bool state)
{
    if (state)
    {
        DL_GPIO_setPins(LED_PORT, LED_PIN);
    }
    else
    {
        DL_GPIO_clearPins(LED_PORT, LED_PIN);
    }
}

/**
 * @brief LED切换状态
 */
void led_toggle(void)
{
    DL_GPIO_togglePins(LED_PORT, LED_PIN);
}

/**
 * @brief 读取按键状态
 */
bool key_read(void)
{
    // 按键按下时为低电平
    return (DL_GPIO_readPins(KEY_PORT, KEY_PIN) == 0);
}

/**
 * @brief 延时函数(毫秒)
 */
void delay_ms(uint32_t ms)
{
    uint32_t start = get_tick_ms();
    while (get_tick_ms() - start < ms)
    {
        // 等待
    }
}

/**
 * @brief 延时函数(微秒)
 */
void delay_us(uint32_t us)
{
    // 简单的循环延时，不够精确但可用
    volatile uint32_t count = us * (SYSTEM_CLOCK_FREQ / 1000000) / 10;
    while (count--)
    {
        __NOP();
    }
}

/**
 * @brief 延时函数(1毫秒)
 */
void delay_1ms(void)
{
    delay_ms(1);
}

/**
 * @brief printf重定向到UART
 */
int lc_printf(const char *format, ...)
{
    char buffer[256];
    va_list args;
    va_start(args, format);
    int len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    // 发送到UART
    for (int i = 0; i < len && i < sizeof(buffer); i++)
    {
        while (!DL_UART_Main_isTXFIFOEmpty(DEBUG_UART));
        DL_UART_Main_transmitData(DEBUG_UART, buffer[i]);
    }
    
    return len;
}

/**
 * @brief 获取系统运行时间(毫秒)
 */
uint32_t get_tick_ms(void)
{
    return system_tick;
}

/**
 * @brief 系统滴答中断处理函数
 */
void systick_handler(void)
{
    system_tick++;
    servo_systick_handler();  // 调用舵机系统的滴答处理函数
}

/**
 * @brief Timer0中断处理函数 - 1ms定时器
 */
void TIMER_0_INST_IRQHandler(void)
{
    switch (DL_TimerG_getPendingInterrupt(TIMER_0_INST))
    {
        case DL_TIMER_IIDX_ZERO:
            systick_handler();
            break;
        default:
            break;
    }
}
